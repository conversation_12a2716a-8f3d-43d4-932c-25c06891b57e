{"_format": "hh-sol-artifact-1", "contractName": "IBLOCKS", "sourceName": "contracts/PackageManagerV2_1.sol", "abi": [{"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}