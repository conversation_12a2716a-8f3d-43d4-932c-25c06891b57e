{"timestamp": "2025-07-12T22:24:07.661Z", "fix": "Exchange rate calculation bug fixed", "deployment": "2025-07-10T23:36:11.584Z", "contracts": {"PackageManagerV2_1": "0xb1995f8C4Cf5409814d191e444e6433f5B6c712b", "BLOCKS": "0xCff8B55324b7c66BD04D66F3AFBFA5A20874c424", "BLOCKS_LP": "0x70C74268f8b22C0c7702b497131ca8025947F0d5", "VestingVault": "0xD79FdE9849a59b1f963A186E569bdBc7814b3d7c", "SwapTaxManager": "0x4d757367e604DbE16116C0aa2F1f20765A415864", "USDT": "0x350eBe9e8030B5C2e70f831b82b92E44569736fF", "PancakeRouter": "0xD99D1c33F9fC3444f8101754aBC46c52416550D1", "PancakeFactory": "0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73", "Treasury": "0x842d803eB7d05D6Aa2DdB8c3Eb912e6d97ce31C4"}, "changes": {"smartContract": "Fixed exchange rate calculation formula in PackageManagerV2_1.sol line 552", "frontend": "Updated contract addresses and removed 1,000,000 division correction logic", "expectedBehavior": {"before": "100 USDT purchase → 50 trillion BLOCKS (inflated)", "after": "100 USDT purchase → 50 BLOCKS (correct for 2 USDT/BLOCKS rate)"}}, "testingRequired": ["Test package purchase with 100 USDT", "Verify portfolio displays realistic values (no more trillion-scale numbers)", "Check ROI calculation shows sensible percentage", "Confirm admin can still configure exchange rates"]}