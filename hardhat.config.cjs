require("dotenv").config();
require("@nomicfoundation/hardhat-toolbox");
require("@nomicfoundation/hardhat-verify");

module.exports = {
  solidity: {
    version: "0.8.20",
    settings: {
      optimizer: { enabled: true, runs: 200 },
      viaIR: true
    },
  },
  networks: {
    bsctestnet: {
      url: process.env.BSC_TESTNET_RPC || "",
      chainId: 97,
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      timeout: 60000,        // 60 seconds timeout
      gasPrice: ***********, // 10 gwei
      gas: 8000000,          // 8M gas limit
      blockGasLimit: ********, // 30M block gas limit
      allowUnlimitedContractSize: true,
      httpHeaders: {
        "User-Agent": "hardhat"
      }
    },
  },
  etherscan: {
    apiKey: process.env.ETHERSCAN_API_KEY || "",
  },
};
