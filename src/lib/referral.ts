import { isAddress } from 'ethers';
import toast from 'react-hot-toast';

/**
 * Referral System Utilities
 * 
 * This module provides utilities for managing referral links, codes, and URL parameters
 * for the BlockCoop referral system.
 */

// URL parameter key for referrer address
export const REFERRER_PARAM = 'ref';

/**
 * Interface for referral link data
 */
export interface ReferralLinkData {
  url: string;
  code: string;
  referrerAddress: string;
}

/**
 * Interface for URL parameters
 */
export interface URLParams {
  [key: string]: string | null;
}

/**
 * Get URL parameters from current location
 */
export function getURLParams(): URLParams {
  const params: URLParams = {};
  const urlParams = new URLSearchParams(window.location.search);
  
  for (const [key, value] of urlParams.entries()) {
    params[key] = value;
  }
  
  return params;
}

/**
 * Get referrer address from URL parameters
 */
export function getReferrerFromURL(): string | null {
  const params = getURLParams();
  const referrer = params[REFERRER_PARAM];
  
  if (referrer && isAddress(referrer)) {
    return referrer;
  }
  
  return null;
}

/**
 * Generate a referral link for a given address
 */
export function generateReferralLink(referrerAddress: string): ReferralLinkData | null {
  if (!isAddress(referrerAddress)) {
    console.error('Invalid referrer address:', referrerAddress);
    return null;
  }

  const baseUrl = window.location.origin;
  const url = `${baseUrl}/?${REFERRER_PARAM}=${referrerAddress}`;
  
  // Generate a short code from the address (first 6 chars after 0x)
  const code = referrerAddress.slice(2, 8).toUpperCase();
  
  return {
    url,
    code,
    referrerAddress,
  };
}

/**
 * Copy referral link to clipboard
 */
export async function copyReferralLink(referralLink: ReferralLinkData): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(referralLink.url);
    toast.success('Referral link copied to clipboard!');
    return true;
  } catch (error) {
    console.error('Failed to copy referral link:', error);
    toast.error('Failed to copy referral link');
    return false;
  }
}

/**
 * Copy referral code to clipboard
 */
export async function copyReferralCode(code: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(code);
    toast.success('Referral code copied to clipboard!');
    return true;
  } catch (error) {
    console.error('Failed to copy referral code:', error);
    toast.error('Failed to copy referral code');
    return false;
  }
}

/**
 * Validate referrer address
 */
export function isValidReferrer(address: string): boolean {
  return isAddress(address);
}

/**
 * Format referrer address for display
 */
export function formatReferrerAddress(address: string): string {
  if (!address) return '';
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

/**
 * Update URL with referrer parameter without page reload
 */
export function updateURLWithReferrer(referrerAddress: string): void {
  if (!isAddress(referrerAddress)) {
    console.error('Invalid referrer address for URL update:', referrerAddress);
    return;
  }

  const url = new URL(window.location.href);
  url.searchParams.set(REFERRER_PARAM, referrerAddress);
  
  // Update URL without page reload
  window.history.replaceState({}, '', url.toString());
}

/**
 * Remove referrer parameter from URL
 */
export function removeReferrerFromURL(): void {
  const url = new URL(window.location.href);
  url.searchParams.delete(REFERRER_PARAM);
  
  // Update URL without page reload
  window.history.replaceState({}, '', url.toString());
}

/**
 * Share referral link via Web Share API (mobile) or fallback to clipboard
 */
export async function shareReferralLink(referralLink: ReferralLinkData): Promise<boolean> {
  const shareData = {
    title: 'Join BlockCoop SACCO',
    text: `Join BlockCoop SACCO using my referral link and start earning with blockchain-powered investments!`,
    url: referralLink.url,
  };

  // Check if Web Share API is supported
  if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
    try {
      await navigator.share(shareData);
      return true;
    } catch (error) {
      // User cancelled sharing or error occurred
      console.log('Share cancelled or failed:', error);
      // Fallback to clipboard
      return await copyReferralLink(referralLink);
    }
  } else {
    // Fallback to clipboard copy
    return await copyReferralLink(referralLink);
  }
}

/**
 * Generate referral statistics summary text
 */
export function generateReferralSummaryText(
  totalRewards: string,
  referralCount: number,
  referralLink: ReferralLinkData
): string {
  return `🎉 My BlockCoop Referral Stats:
💰 Total Rewards: ${totalRewards} BLOCKS
👥 Referrals: ${referralCount}
🔗 Join using my link: ${referralLink.url}

#BlockCoop #SACCO #DeFi #Referral`;
}

/**
 * Interface for referral transaction data
 */
export interface ReferralTransaction {
  referrer: string;
  buyer: string;
  reward: bigint;
  timestamp: number;
  blockNumber: number;
  transactionHash: string;
}

/**
 * Interface for referral statistics
 */
export interface ReferralStats {
  totalRewards: bigint;
  referralCount: number;
  transactions: ReferralTransaction[];
}

/**
 * Interface for referral performance metrics
 */
export interface ReferralPerformance {
  totalRewards: bigint;
  totalReferrals: number;
  averageReward: bigint;
  lastReferralDate: number | null;
  topReferralReward: bigint;
  recentActivity: ReferralTransaction[];
}
