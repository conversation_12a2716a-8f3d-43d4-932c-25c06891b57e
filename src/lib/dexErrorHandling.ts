import { ethers } from 'ethers';
import toast from 'react-hot-toast';

// Error types for DEX operations
export enum DEXErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  INSUFFICIENT_ALLOWANCE = 'INSUFFICIENT_ALLOWANCE',
  SLIPPAGE_EXCEEDED = 'SLIPPAGE_EXCEEDED',
  DEADLINE_EXCEEDED = 'DEADLINE_EXCEEDED',
  PAIR_NOT_EXISTS = 'PAIR_NOT_EXISTS',
  INSUFFICIENT_LIQUIDITY = 'INSUFFICIENT_LIQUIDITY',
  TRANSACTION_FAILED = 'TRANSACTION_FAILED',
  USER_REJECTED = 'USER_REJECTED',
  INVALID_AMOUNT = 'INVALID_AMOUNT',
  PRICE_IMPACT_TOO_HIGH = 'PRICE_IMPACT_TOO_HIGH',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface DEXError {
  type: DEXErrorType;
  message: string;
  originalError?: any;
  userMessage: string;
  actionable: boolean;
  suggestedAction?: string;
}

// Error patterns to match against
const ERROR_PATTERNS = [
  {
    pattern: /user rejected/i,
    type: DEXErrorType.USER_REJECTED,
    userMessage: 'Transaction was cancelled by user',
    actionable: false,
  },
  {
    pattern: /insufficient funds|insufficient balance/i,
    type: DEXErrorType.INSUFFICIENT_BALANCE,
    userMessage: 'Insufficient token balance for this transaction',
    actionable: true,
    suggestedAction: 'Add more tokens to your wallet or reduce the amount',
  },
  {
    pattern: /insufficient allowance/i,
    type: DEXErrorType.INSUFFICIENT_ALLOWANCE,
    userMessage: 'Token allowance not approved',
    actionable: true,
    suggestedAction: 'Approve token spending and try again',
  },
  {
    pattern: /slippage|price impact/i,
    type: DEXErrorType.SLIPPAGE_EXCEEDED,
    userMessage: 'Price changed too much during transaction',
    actionable: true,
    suggestedAction: 'Increase slippage tolerance or try again',
  },
  {
    pattern: /deadline|expired/i,
    type: DEXErrorType.DEADLINE_EXCEEDED,
    userMessage: 'Transaction took too long to process',
    actionable: true,
    suggestedAction: 'Try again with a longer deadline',
  },
  {
    pattern: /pair.*not.*exist|no pair/i,
    type: DEXErrorType.PAIR_NOT_EXISTS,
    userMessage: 'Trading pair does not exist',
    actionable: false,
  },
  {
    pattern: /insufficient.*liquidity/i,
    type: DEXErrorType.INSUFFICIENT_LIQUIDITY,
    userMessage: 'Not enough liquidity in the pool for this trade',
    actionable: true,
    suggestedAction: 'Reduce trade amount or add liquidity to the pool',
  },
  {
    pattern: /network|connection|timeout/i,
    type: DEXErrorType.NETWORK_ERROR,
    userMessage: 'Network connection issue',
    actionable: true,
    suggestedAction: 'Check your internet connection and try again',
  },
  {
    pattern: /transaction failed|reverted/i,
    type: DEXErrorType.TRANSACTION_FAILED,
    userMessage: 'Transaction failed on the blockchain',
    actionable: true,
    suggestedAction: 'Check transaction details and try again',
  },
];

/**
 * Parse and categorize DEX-related errors
 */
export function parseDEXError(error: any): DEXError {
  const errorMessage = error?.message || error?.reason || String(error);
  
  // Check for known error patterns
  for (const pattern of ERROR_PATTERNS) {
    if (pattern.pattern.test(errorMessage)) {
      return {
        type: pattern.type,
        message: errorMessage,
        originalError: error,
        userMessage: pattern.userMessage,
        actionable: pattern.actionable,
        suggestedAction: pattern.suggestedAction,
      };
    }
  }

  // Handle specific ethers errors
  if (error?.code) {
    switch (error.code) {
      case 'ACTION_REJECTED':
      case 4001:
        return {
          type: DEXErrorType.USER_REJECTED,
          message: errorMessage,
          originalError: error,
          userMessage: 'Transaction was cancelled',
          actionable: false,
        };
      
      case 'INSUFFICIENT_FUNDS':
        return {
          type: DEXErrorType.INSUFFICIENT_BALANCE,
          message: errorMessage,
          originalError: error,
          userMessage: 'Insufficient funds for transaction',
          actionable: true,
          suggestedAction: 'Add more tokens to your wallet',
        };
      
      case 'NETWORK_ERROR':
        return {
          type: DEXErrorType.NETWORK_ERROR,
          message: errorMessage,
          originalError: error,
          userMessage: 'Network connection failed',
          actionable: true,
          suggestedAction: 'Check your connection and try again',
        };
    }
  }

  // Default unknown error
  return {
    type: DEXErrorType.UNKNOWN_ERROR,
    message: errorMessage,
    originalError: error,
    userMessage: 'An unexpected error occurred',
    actionable: true,
    suggestedAction: 'Please try again or contact support',
  };
}

/**
 * Handle DEX errors with appropriate user feedback
 */
export function handleDEXError(error: any, operation: string = 'operation'): DEXError {
  const parsedError = parseDEXError(error);
  
  console.error(`DEX ${operation} error:`, {
    type: parsedError.type,
    message: parsedError.message,
    originalError: parsedError.originalError,
  });

  // Show appropriate toast message
  if (parsedError.type === DEXErrorType.USER_REJECTED) {
    // Don't show toast for user rejections
    return parsedError;
  }

  const toastMessage = parsedError.suggestedAction 
    ? `${parsedError.userMessage}. ${parsedError.suggestedAction}`
    : parsedError.userMessage;

  toast.error(toastMessage, {
    duration: parsedError.actionable ? 6000 : 4000,
    id: `dex-error-${operation}`,
  });

  return parsedError;
}

/**
 * Validate transaction parameters before execution
 */
export function validateTransactionParams(params: {
  amount?: bigint;
  balance?: bigint;
  allowance?: bigint;
  slippage?: number;
  deadline?: number;
}): DEXError | null {
  const { amount, balance, allowance, slippage, deadline } = params;

  // Validate amount
  if (amount !== undefined && amount <= 0n) {
    return {
      type: DEXErrorType.INVALID_AMOUNT,
      message: 'Amount must be greater than zero',
      userMessage: 'Please enter a valid amount',
      actionable: true,
      suggestedAction: 'Enter an amount greater than zero',
    };
  }

  // Validate balance
  if (amount !== undefined && balance !== undefined && amount > balance) {
    return {
      type: DEXErrorType.INSUFFICIENT_BALANCE,
      message: 'Amount exceeds available balance',
      userMessage: 'Insufficient balance for this transaction',
      actionable: true,
      suggestedAction: 'Reduce the amount or add more tokens to your wallet',
    };
  }

  // Validate allowance
  if (amount !== undefined && allowance !== undefined && amount > allowance) {
    return {
      type: DEXErrorType.INSUFFICIENT_ALLOWANCE,
      message: 'Amount exceeds approved allowance',
      userMessage: 'Token allowance needs to be approved',
      actionable: true,
      suggestedAction: 'Approve token spending first',
    };
  }

  // Validate slippage
  if (slippage !== undefined && (slippage < 0.01 || slippage > 50)) {
    return {
      type: DEXErrorType.INVALID_AMOUNT,
      message: 'Invalid slippage tolerance',
      userMessage: 'Slippage tolerance must be between 0.01% and 50%',
      actionable: true,
      suggestedAction: 'Set slippage between 0.01% and 50%',
    };
  }

  // Validate deadline
  if (deadline !== undefined && deadline < Math.floor(Date.now() / 1000)) {
    return {
      type: DEXErrorType.DEADLINE_EXCEEDED,
      message: 'Deadline is in the past',
      userMessage: 'Transaction deadline has already passed',
      actionable: true,
      suggestedAction: 'Set a future deadline',
    };
  }

  return null;
}

/**
 * Create loading state manager for DEX operations
 */
export function createLoadingManager() {
  const loadingStates = new Map<string, boolean>();
  const listeners = new Set<(states: Map<string, boolean>) => void>();

  return {
    setLoading: (operation: string, loading: boolean) => {
      loadingStates.set(operation, loading);
      listeners.forEach(listener => listener(new Map(loadingStates)));
    },
    
    isLoading: (operation: string) => loadingStates.get(operation) || false,
    
    isAnyLoading: () => Array.from(loadingStates.values()).some(Boolean),
    
    subscribe: (listener: (states: Map<string, boolean>) => void) => {
      listeners.add(listener);
      return () => listeners.delete(listener);
    },
    
    clear: () => {
      loadingStates.clear();
      listeners.forEach(listener => listener(new Map()));
    },
  };
}

/**
 * Retry mechanism for failed operations
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      const parsedError = parseDEXError(error);
      
      // Don't retry user rejections or certain error types
      if (parsedError.type === DEXErrorType.USER_REJECTED ||
          parsedError.type === DEXErrorType.INSUFFICIENT_BALANCE ||
          parsedError.type === DEXErrorType.PAIR_NOT_EXISTS) {
        throw error;
      }
      
      if (attempt < maxRetries) {
        console.log(`Operation failed, retrying in ${delay}ms (attempt ${attempt}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
      }
    }
  }
  
  throw lastError;
}
