/**
 * DEX Integration Test Suite
 * Tests all DEX functionality on BSC Testnet
 */

import { ethers } from 'ethers';
import { appKitConfig } from '../lib/appkit';
import { createDEXManager } from '../lib/dexUtils';
import { getPoolInfo, getSwapQuote, getLiquidityQuote } from '../lib/contracts';

// Test configuration
const TEST_CONFIG = {
  // Test amounts (in token units)
  SHARE_TOKEN_AMOUNT: '1.0', // 1 ShareToken
  USDT_AMOUNT: '10.0', // 10 USDT
  SMALL_SWAP_AMOUNT: '0.1', // 0.1 tokens for swap test
  
  // Test addresses (replace with actual test addresses)
  TEST_PRIVATE_KEY: process.env.TEST_PRIVATE_KEY || '',
  BSC_TESTNET_RPC: 'https://data-seed-prebsc-1-s1.binance.org:8545/',
  
  // Expected contract addresses
  EXPECTED_CONTRACTS: {
    router: '******************************************',
    factory: '******************************************',
  },
};

interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  details?: any;
}

class DEXIntegrationTester {
  private provider: ethers.JsonRpcProvider;
  private signer: ethers.Wallet;
  private dexManager: any;
  private results: TestResult[] = [];

  constructor() {
    this.provider = new ethers.JsonRpcProvider(TEST_CONFIG.BSC_TESTNET_RPC);
    
    if (!TEST_CONFIG.TEST_PRIVATE_KEY) {
      throw new Error('TEST_PRIVATE_KEY environment variable is required');
    }
    
    this.signer = new ethers.Wallet(TEST_CONFIG.TEST_PRIVATE_KEY, this.provider);
    this.dexManager = createDEXManager(this.signer);
  }

  private addResult(name: string, passed: boolean, error?: string, details?: any) {
    this.results.push({ name, passed, error, details });
    console.log(`${passed ? '✅' : '❌'} ${name}${error ? `: ${error}` : ''}`);
  }

  async testContractConfiguration() {
    console.log('\n🔧 Testing Contract Configuration...');
    
    try {
      // Test router address
      const routerAddress = appKitConfig.contracts.router;
      const expectedRouter = TEST_CONFIG.EXPECTED_CONTRACTS.router;
      
      this.addResult(
        'Router Address Configuration',
        routerAddress.toLowerCase() === expectedRouter.toLowerCase(),
        routerAddress !== expectedRouter ? `Expected ${expectedRouter}, got ${routerAddress}` : undefined
      );

      // Test factory address
      const factoryAddress = appKitConfig.contracts.factory;
      const expectedFactory = TEST_CONFIG.EXPECTED_CONTRACTS.factory;
      
      this.addResult(
        'Factory Address Configuration',
        factoryAddress.toLowerCase() === expectedFactory.toLowerCase(),
        factoryAddress !== expectedFactory ? `Expected ${expectedFactory}, got ${factoryAddress}` : undefined
      );

      // Test contract addresses are valid
      const contracts = [
        'usdt', 'share', 'lp', 'vault', 'taxManager', 'router', 'factory', 'packageManager'
      ];
      
      for (const contract of contracts) {
        const address = appKitConfig.contracts[contract as keyof typeof appKitConfig.contracts];
        const isValid = ethers.isAddress(address);
        this.addResult(
          `${contract} Address Validity`,
          isValid,
          !isValid ? `Invalid address: ${address}` : undefined
        );
      }

    } catch (error) {
      this.addResult('Contract Configuration', false, (error as Error).message);
    }
  }

  async testPoolInformation() {
    console.log('\n📊 Testing Pool Information...');
    
    try {
      // Test pool info retrieval
      const poolInfo = await getPoolInfo(this.signer.address);
      
      this.addResult(
        'Pool Info Retrieval',
        !!poolInfo,
        !poolInfo ? 'Failed to retrieve pool information' : undefined,
        poolInfo ? {
          pairAddress: poolInfo.pairAddress,
          totalLiquidity: poolInfo.totalLiquidity.toString(),
          shareTokenPrice: poolInfo.shareTokenPrice,
        } : undefined
      );

      if (poolInfo) {
        // Test pool has liquidity
        const hasLiquidity = poolInfo.reserves.reserveShare > 0n && poolInfo.reserves.reserveUSDT > 0n;
        this.addResult(
          'Pool Has Liquidity',
          hasLiquidity,
          !hasLiquidity ? 'Pool has no liquidity' : undefined,
          {
            shareReserve: poolInfo.reserves.reserveShare.toString(),
            usdtReserve: poolInfo.reserves.reserveUSDT.toString(),
          }
        );

        // Test price calculation
        const priceIsValid = poolInfo.shareTokenPrice > 0 && poolInfo.usdtPrice > 0;
        this.addResult(
          'Price Calculation',
          priceIsValid,
          !priceIsValid ? 'Invalid price calculation' : undefined,
          {
            shareTokenPrice: poolInfo.shareTokenPrice,
            usdtPrice: poolInfo.usdtPrice,
          }
        );
      }

    } catch (error) {
      this.addResult('Pool Information', false, (error as Error).message);
    }
  }

  async testSwapQuotes() {
    console.log('\n💱 Testing Swap Quotes...');
    
    try {
      // Test ShareToken to USDT quote
      const shareAmount = ethers.parseUnits(TEST_CONFIG.SMALL_SWAP_AMOUNT, 18);
      const shareToUsdtQuote = await getSwapQuote('share', shareAmount);
      
      this.addResult(
        'ShareToken to USDT Quote',
        !!shareToUsdtQuote && shareToUsdtQuote.amountOut > 0n,
        !shareToUsdtQuote ? 'Failed to get quote' : undefined,
        shareToUsdtQuote ? {
          amountIn: shareToUsdtQuote.amountIn.toString(),
          amountOut: shareToUsdtQuote.amountOut.toString(),
          priceImpact: shareToUsdtQuote.priceImpact,
        } : undefined
      );

      // Test USDT to ShareToken quote
      const usdtAmount = ethers.parseUnits(TEST_CONFIG.SMALL_SWAP_AMOUNT, 6);
      const usdtToShareQuote = await getSwapQuote('usdt', usdtAmount);
      
      this.addResult(
        'USDT to ShareToken Quote',
        !!usdtToShareQuote && usdtToShareQuote.amountOut > 0n,
        !usdtToShareQuote ? 'Failed to get quote' : undefined,
        usdtToShareQuote ? {
          amountIn: usdtToShareQuote.amountIn.toString(),
          amountOut: usdtToShareQuote.amountOut.toString(),
          priceImpact: usdtToShareQuote.priceImpact,
        } : undefined
      );

      // Test price impact calculation
      if (shareToUsdtQuote) {
        const reasonablePriceImpact = shareToUsdtQuote.priceImpact < 10; // Less than 10%
        this.addResult(
          'Reasonable Price Impact',
          reasonablePriceImpact,
          !reasonablePriceImpact ? `High price impact: ${shareToUsdtQuote.priceImpact}%` : undefined
        );
      }

    } catch (error) {
      this.addResult('Swap Quotes', false, (error as Error).message);
    }
  }

  async testLiquidityQuotes() {
    console.log('\n🏊 Testing Liquidity Quotes...');
    
    try {
      // Test liquidity quote
      const shareAmount = ethers.parseUnits(TEST_CONFIG.SHARE_TOKEN_AMOUNT, 18);
      const usdtAmount = ethers.parseUnits(TEST_CONFIG.USDT_AMOUNT, 6);
      
      const liquidityQuote = await getLiquidityQuote(shareAmount, usdtAmount);
      
      this.addResult(
        'Liquidity Quote Calculation',
        !!liquidityQuote && liquidityQuote.lpTokensReceived > 0n,
        !liquidityQuote ? 'Failed to get liquidity quote' : undefined,
        liquidityQuote ? {
          shareTokenAmount: liquidityQuote.shareTokenAmount.toString(),
          usdtAmount: liquidityQuote.usdtAmount.toString(),
          lpTokensReceived: liquidityQuote.lpTokensReceived.toString(),
          shareOfPool: liquidityQuote.shareOfPool,
        } : undefined
      );

      if (liquidityQuote) {
        // Test optimal amount calculation
        const amountsAreOptimal = 
          liquidityQuote.shareTokenAmount <= shareAmount &&
          liquidityQuote.usdtAmount <= usdtAmount;
        
        this.addResult(
          'Optimal Amount Calculation',
          amountsAreOptimal,
          !amountsAreOptimal ? 'Calculated amounts exceed input amounts' : undefined
        );
      }

    } catch (error) {
      this.addResult('Liquidity Quotes', false, (error as Error).message);
    }
  }

  async testErrorHandling() {
    console.log('\n🚨 Testing Error Handling...');
    
    try {
      // Test invalid amount (zero)
      try {
        await getSwapQuote('share', 0n);
        this.addResult('Zero Amount Error Handling', false, 'Should have thrown error for zero amount');
      } catch (error) {
        this.addResult('Zero Amount Error Handling', true);
      }

      // Test very large amount (should cause insufficient liquidity)
      try {
        const largeAmount = ethers.parseUnits('1000000', 18); // 1M tokens
        await getSwapQuote('share', largeAmount);
        this.addResult('Large Amount Error Handling', false, 'Should have thrown error for excessive amount');
      } catch (error) {
        this.addResult('Large Amount Error Handling', true);
      }

    } catch (error) {
      this.addResult('Error Handling', false, (error as Error).message);
    }
  }

  async testNetworkConnectivity() {
    console.log('\n🌐 Testing Network Connectivity...');
    
    try {
      // Test provider connection
      const blockNumber = await this.provider.getBlockNumber();
      this.addResult(
        'Provider Connection',
        blockNumber > 0,
        blockNumber <= 0 ? 'Failed to get block number' : undefined,
        { blockNumber }
      );

      // Test signer balance
      const balance = await this.provider.getBalance(this.signer.address);
      this.addResult(
        'Test Account Balance',
        balance > 0n,
        balance <= 0n ? 'Test account has no BNB for gas' : undefined,
        { balance: ethers.formatEther(balance) }
      );

    } catch (error) {
      this.addResult('Network Connectivity', false, (error as Error).message);
    }
  }

  async runAllTests() {
    console.log('🧪 Starting DEX Integration Tests...\n');
    console.log(`Test Account: ${this.signer.address}`);
    console.log(`Network: BSC Testnet (Chain ID: 97)\n`);

    await this.testNetworkConnectivity();
    await this.testContractConfiguration();
    await this.testPoolInformation();
    await this.testSwapQuotes();
    await this.testLiquidityQuotes();
    await this.testErrorHandling();

    this.printSummary();
  }

  printSummary() {
    console.log('\n📋 Test Summary:');
    console.log('================');
    
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const percentage = Math.round((passed / total) * 100);
    
    console.log(`✅ Passed: ${passed}/${total} (${percentage}%)`);
    console.log(`❌ Failed: ${total - passed}/${total}`);
    
    const failed = this.results.filter(r => !r.passed);
    if (failed.length > 0) {
      console.log('\n❌ Failed Tests:');
      failed.forEach(test => {
        console.log(`  - ${test.name}: ${test.error}`);
      });
    }
    
    console.log('\n🎯 Recommendations:');
    if (percentage >= 90) {
      console.log('✅ DEX integration is ready for production!');
    } else if (percentage >= 70) {
      console.log('⚠️  DEX integration needs minor fixes before production.');
    } else {
      console.log('🚨 DEX integration requires significant fixes before production.');
    }
  }
}

// Export for use in other test files
export { DEXIntegrationTester, TEST_CONFIG };

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new DEXIntegrationTester();
  tester.runAllTests().catch(console.error);
}
