import { useState, useCallback, useEffect } from 'react';
import { ethers } from 'ethers';
import { useWeb3 } from '../providers/Web3Provider';
import { useRefreshListener } from '../contexts/RefreshContext';
import { handleDEXError, validateTransactionParams, retryOperation, DEXError } from '../lib/dexErrorHandling';
import {
  getPoolInfo,
  getSwapQuote,
  getLiquidityQuote,
  executeSwap,
  executeAddLiquidity,
  checkTokenAllowances,
} from '../lib/contracts';
import {
  PoolInfo,
  SwapQuote,
  LiquidityQuote,
  SwapParams,
  AddLiquidityParams,
  SlippageConfig,
  DEFAULT_SLIPPAGE_CONFIG,
} from '../lib/dexUtils';

// Hook for pool information
export function usePoolInfo() {
  const { account, isConnected } = useWeb3();
  const [poolInfo, setPoolInfo] = useState<PoolInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<DEXError | null>(null);

  const fetchPoolInfo = useCallback(async () => {
    if (!isConnected) {
      setPoolInfo(null);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const info = await retryOperation(
        () => getPoolInfo(account || undefined),
        2, // Retry up to 2 times
        1000 // 1 second delay
      );
      setPoolInfo(info);
    } catch (err) {
      const dexError = handleDEXError(err, 'fetch pool info');
      setError(dexError);
    } finally {
      setLoading(false);
    }
  }, [account, isConnected]);

  // Auto-refresh on connection changes
  useEffect(() => {
    fetchPoolInfo();
  }, [fetchPoolInfo]);

  // Listen for refresh events
  useRefreshListener('refreshPoolInfo', fetchPoolInfo);

  return {
    poolInfo,
    loading,
    error,
    refetch: fetchPoolInfo,
    clearError: () => setError(null),
  };
}

// Hook for swap operations
export function useSwap() {
  const { signer, account, isConnected } = useWeb3();
  const [swapQuote, setSwapQuote] = useState<SwapQuote | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<DEXError | null>(null);

  const getQuote = useCallback(async (
    tokenIn: 'share' | 'usdt',
    amountIn: bigint,
    slippageConfig: SlippageConfig = DEFAULT_SLIPPAGE_CONFIG
  ) => {
    if (!isConnected || amountIn <= 0n) {
      setSwapQuote(null);
      setError(null);
      return;
    }

    // Validate parameters
    const validationError = validateTransactionParams({
      amount: amountIn,
      slippage: slippageConfig.tolerance,
      deadline: Math.floor(Date.now() / 1000) + (slippageConfig.deadline * 60),
    });

    if (validationError) {
      setError(validationError);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const quote = await retryOperation(
        () => getSwapQuote(tokenIn, amountIn, slippageConfig),
        2,
        500
      );
      setSwapQuote(quote);
    } catch (err) {
      const dexError = handleDEXError(err, 'get swap quote');
      setError(dexError);
    } finally {
      setLoading(false);
    }
  }, [isConnected]);

  const executeSwapTransaction = useCallback(async (params: SwapParams) => {
    if (!signer || !account) {
      const error = new Error('Wallet not connected');
      const dexError = handleDEXError(error, 'execute swap');
      setError(dexError);
      throw error;
    }

    // Validate parameters before execution
    const validationError = validateTransactionParams({
      amount: params.amountIn,
      deadline: params.deadline,
    });

    if (validationError) {
      setError(validationError);
      throw new Error(validationError.message);
    }

    setLoading(true);
    setError(null);

    try {
      const tx = await executeSwap(params, signer);
      setLoading(false);
      return tx;
    } catch (err) {
      const dexError = handleDEXError(err, 'execute swap');
      setError(dexError);
      setLoading(false);
      throw err;
    }
  }, [signer, account]);

  const clearQuote = useCallback(() => {
    setSwapQuote(null);
    setError(null);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    swapQuote,
    loading,
    error,
    getQuote,
    executeSwap: executeSwapTransaction,
    clearQuote,
    clearError,
  };
}

// Hook for liquidity operations
export function useLiquidity() {
  const { signer, account, isConnected } = useWeb3();
  const [liquidityQuote, setLiquidityQuote] = useState<LiquidityQuote | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getQuote = useCallback(async (
    shareTokenAmount: bigint,
    usdtAmount: bigint
  ) => {
    if (!isConnected || shareTokenAmount <= 0n || usdtAmount <= 0n) {
      setLiquidityQuote(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const quote = await getLiquidityQuote(shareTokenAmount, usdtAmount);
      setLiquidityQuote(quote);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get liquidity quote';
      setError(errorMessage);
      console.error('Error getting liquidity quote:', err);
    } finally {
      setLoading(false);
    }
  }, [isConnected]);

  const executeAddLiquidityTransaction = useCallback(async (params: AddLiquidityParams) => {
    if (!signer || !account) {
      throw new Error('Wallet not connected');
    }

    setLoading(true);
    setError(null);

    try {
      const tx = await executeAddLiquidity(params, signer);
      setLoading(false);
      return tx;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add liquidity';
      setError(errorMessage);
      setLoading(false);
      throw err;
    }
  }, [signer, account]);

  const clearQuote = useCallback(() => {
    setLiquidityQuote(null);
    setError(null);
  }, []);

  return {
    liquidityQuote,
    loading,
    error,
    getQuote,
    executeAddLiquidity: executeAddLiquidityTransaction,
    clearQuote,
  };
}

// Hook for token allowances
export function useTokenAllowances() {
  const { account, isConnected } = useWeb3();
  const [allowances, setAllowances] = useState<{
    shareToken: bigint;
    usdt: bigint;
  }>({
    shareToken: 0n,
    usdt: 0n,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkAllowances = useCallback(async (
    shareTokenAmount: bigint,
    usdtAmount: bigint
  ) => {
    if (!account || !isConnected) {
      setAllowances({ shareToken: 0n, usdt: 0n });
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await checkTokenAllowances(shareTokenAmount, usdtAmount, account);
      // This is a simplified version - in practice, you'd want to get actual allowance values
      setAllowances({
        shareToken: shareTokenAmount, // Placeholder
        usdt: usdtAmount, // Placeholder
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to check allowances';
      setError(errorMessage);
      console.error('Error checking allowances:', err);
    } finally {
      setLoading(false);
    }
  }, [account, isConnected]);

  return {
    allowances,
    loading,
    error,
    checkAllowances,
  };
}

// Hook for DEX statistics and formatting
export function useDEXFormatting() {
  const formatTokenAmount = useCallback((amount: bigint, decimals: number, displayDecimals: number = 4): string => {
    const divisor = 10n ** BigInt(decimals);
    const wholePart = amount / divisor;
    const fractionalPart = amount % divisor;
    
    const fractionalStr = fractionalPart.toString().padStart(decimals, '0');
    const truncatedFractional = fractionalStr.slice(0, displayDecimals);
    
    return `${wholePart}.${truncatedFractional}`;
  }, []);

  const formatPercentage = useCallback((value: number, decimals: number = 2): string => {
    return `${value.toFixed(decimals)}%`;
  }, []);

  const formatPriceImpact = useCallback((impact: number): string => {
    const color = impact > 5 ? 'text-red-600' : impact > 2 ? 'text-yellow-600' : 'text-green-600';
    return { value: formatPercentage(impact), color };
  }, [formatPercentage]);

  const parseTokenAmount = useCallback((amount: string, decimals: number): bigint => {
    try {
      return ethers.parseUnits(amount, decimals);
    } catch (error) {
      throw new Error(`Invalid amount: ${amount}`);
    }
  }, []);

  return {
    formatTokenAmount,
    formatPercentage,
    formatPriceImpact,
    parseTokenAmount,
  };
}
