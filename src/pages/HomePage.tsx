
import { PackageList } from '../components/packages/PackageList';
import { Card, CardContent } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import {
  Shield,
  Zap,
  Package,
  Split,
  ArrowRight,
  Users,
  Target,
  CheckCircle,
  Lock,
  BarChart3,
  Coins,
  Globe,
  Award,
  Handshake
} from 'lucide-react';

export function HomePage() {
  const scrollToPackages = () => {
    const packagesSection = document.getElementById('packages-section');
    if (packagesSection) {
      packagesSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const scrollToHowItWorks = () => {
    const howItWorksSection = document.getElementById('how-it-works-section');
    if (howItWorksSection) {
      howItWorksSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <div className="text-center space-y-8">
        <div className="space-y-6">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight">
            The Future of
            <span className="text-primary-600 block">SACCO Share Trading</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            BlockCoop revolutionizes traditional SACCO investments with blockchain technology.
            Purchase investment packages that automatically split between vesting schedules and
            liquidity pools, giving you both long-term growth and immediate trading flexibility.
          </p>
        </div>

        <div className="flex flex-wrap justify-center gap-8 text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <Package className="h-5 w-5 text-primary-600" />
            <span>Smart Packages</span>
          </div>
          <div className="flex items-center space-x-2">
            <Split className="h-5 w-5 text-accent-600" />
            <span>Auto-Splitting</span>
          </div>
          <div className="flex items-center space-x-2">
            <Zap className="h-5 w-5 text-warning-500" />
            <span>Instant Liquidity</span>
          </div>
          <div className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-success-600" />
            <span>Secure Vesting</span>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-8">
          <Button size="lg" className="px-8 py-4" onClick={scrollToPackages}>
            Explore Packages
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
          <Button variant="outline" size="lg" className="px-8 py-4" onClick={scrollToHowItWorks}>
            Learn How It Works
          </Button>
        </div>
      </div>

      {/* Key Features Section */}
      <div className="space-y-12">
        <div className="text-center space-y-4">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
            Why Choose BlockCoop?
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Experience the perfect blend of traditional SACCO principles and cutting-edge blockchain technology
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
          <Card className="text-center group hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6 space-y-4">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                <Package className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Smart Investment Packages</h3>
              <p className="text-gray-600 text-sm">
                Pre-configured investment options with optimized split ratios for different risk profiles and investment goals.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center group hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6 space-y-4">
              <div className="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                <Split className="h-8 w-8 text-accent-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Automated Splitting</h3>
              <p className="text-gray-600 text-sm">
                Your investment automatically splits between vesting schedules and liquidity pools for balanced growth.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center group hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6 space-y-4">
              <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                <Shield className="h-8 w-8 text-success-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Secure Vesting</h3>
              <p className="text-gray-600 text-sm">
                Time-locked smart contracts ensure your long-term investments are protected with predictable release schedules.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center group hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6 space-y-4">
              <div className="w-16 h-16 bg-warning-100 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                <Zap className="h-8 w-8 text-warning-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Instant Liquidity</h3>
              <p className="text-gray-600 text-sm">
                Access immediate liquidity through LP tokens that can be redeemed or traded on secondary markets anytime.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center group hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6 space-y-4">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                <BarChart3 className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Secondary Market Trading</h3>
              <p className="text-gray-600 text-sm">
                Trade your share tokens on integrated DEX platforms for maximum flexibility and price discovery.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* How It Works Section */}
      <div id="how-it-works-section" className="space-y-12">
        <div className="text-center space-y-4">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
            How It Works
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Simple, transparent, and automated - get started in three easy steps
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center space-y-6">
            <div className="relative">
              <div className="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center mx-auto">
                <Package className="h-10 w-10 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-accent-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">1</span>
              </div>
            </div>
            <div className="space-y-3">
              <h3 className="text-xl font-semibold text-gray-900">Choose Your Package</h3>
              <p className="text-gray-600">
                Select from our range of investment packages designed for different risk profiles and investment goals.
                Each package has pre-configured split ratios and vesting schedules.
              </p>
            </div>
          </div>

          <div className="text-center space-y-6">
            <div className="relative">
              <div className="w-20 h-20 bg-accent-600 rounded-full flex items-center justify-center mx-auto">
                <Split className="h-10 w-10 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-accent-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">2</span>
              </div>
            </div>
            <div className="space-y-3">
              <h3 className="text-xl font-semibold text-gray-900">Automatic Splitting</h3>
              <p className="text-gray-600">
                Your USDT investment is automatically split: part goes to the vesting vault for long-term growth,
                part creates liquidity pools, and you receive LP tokens for immediate flexibility.
              </p>
            </div>
          </div>

          <div className="text-center space-y-6">
            <div className="relative">
              <div className="w-20 h-20 bg-warning-600 rounded-full flex items-center justify-center mx-auto">
                <Coins className="h-10 w-10 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-accent-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">3</span>
              </div>
            </div>
            <div className="space-y-3">
              <h3 className="text-xl font-semibold text-gray-900">Claim & Trade</h3>
              <p className="text-gray-600">
                Claim your vested BLOCKS tokens as they unlock over time, or trade your BLOCKS-LP tokens on secondary markets
                for immediate liquidity. Full control, maximum flexibility.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Why Trade with BlockCoop Section */}
      <div className="bg-gradient-to-r from-primary-50 to-accent-50 rounded-2xl p-8 md:p-12">
        <div className="space-y-8">
          <div className="text-center space-y-4">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Why Trade with BlockCoop?
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Experience the advantages of blockchain-powered SACCO investments
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Transparent & Secure</h3>
                <p className="text-gray-600 text-sm">
                  All transactions are recorded on the blockchain, ensuring complete transparency and security for your investments.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-accent-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <Globe className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Global Accessibility</h3>
                <p className="text-gray-600 text-sm">
                  Access your investments 24/7 from anywhere in the world with just an internet connection and wallet.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-warning-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Instant Settlements</h3>
                <p className="text-gray-600 text-sm">
                  No waiting for bank transfers or clearing houses - transactions settle instantly on the blockchain.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-success-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Flexible Investment Options</h3>
                <p className="text-gray-600 text-sm">
                  Choose from multiple packages with different risk profiles, vesting periods, and liquidity options.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Community-Driven</h3>
                <p className="text-gray-600 text-sm">
                  Built on SACCO principles of mutual cooperation and shared prosperity within a decentralized framework.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-accent-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <Award className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Proven Technology</h3>
                <p className="text-gray-600 text-sm">
                  Built on Binance Smart Chain with battle-tested smart contracts and comprehensive security audits.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Packages Section */}
      <div id="packages-section" className="space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Investment Packages</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Choose from our carefully crafted investment packages, each designed with unique split ratios and vesting schedules.
          </p>
        </div>

        <PackageList />
      </div>

      {/* Target Audience Section */}
      <div className="space-y-12">
        <div className="text-center space-y-4">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
            Perfect for Every Investor
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Whether you're new to DeFi or a seasoned investor, BlockCoop has something for you
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card className="group hover:shadow-xl transition-all duration-300">
            <CardContent className="p-8 text-center space-y-6">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                <Target className="h-8 w-8 text-primary-600" />
              </div>
              <div className="space-y-3">
                <h3 className="text-xl font-semibold text-gray-900">DeFi Enthusiasts</h3>
                <p className="text-gray-600">
                  Explore advanced splitting mechanisms, automated liquidity provision, and yield optimization
                  strategies in a secure, audited environment.
                </p>
              </div>
              <div className="flex flex-wrap gap-2 justify-center">
                <span className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-xs font-medium">
                  Advanced Features
                </span>
                <span className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-xs font-medium">
                  High Yields
                </span>
              </div>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300">
            <CardContent className="p-8 text-center space-y-6">
              <div className="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                <Handshake className="h-8 w-8 text-accent-600" />
              </div>
              <div className="space-y-3">
                <h3 className="text-xl font-semibold text-gray-900">Traditional Investors</h3>
                <p className="text-gray-600">
                  Experience familiar SACCO investment principles enhanced with blockchain transparency,
                  security, and global accessibility.
                </p>
              </div>
              <div className="flex flex-wrap gap-2 justify-center">
                <span className="px-3 py-1 bg-accent-100 text-accent-700 rounded-full text-xs font-medium">
                  Familiar Concepts
                </span>
                <span className="px-3 py-1 bg-accent-100 text-accent-700 rounded-full text-xs font-medium">
                  Enhanced Security
                </span>
              </div>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300">
            <CardContent className="p-8 text-center space-y-6">
              <div className="w-16 h-16 bg-warning-100 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                <Users className="h-8 w-8 text-warning-600" />
              </div>
              <div className="space-y-3">
                <h3 className="text-xl font-semibold text-gray-900">SACCO Members</h3>
                <p className="text-gray-600">
                  Modernize your cooperative investments with blockchain technology while maintaining
                  the community-driven values you know and trust.
                </p>
              </div>
              <div className="flex flex-wrap gap-2 justify-center">
                <span className="px-3 py-1 bg-warning-100 text-warning-700 rounded-full text-xs font-medium">
                  Community Focus
                </span>
                <span className="px-3 py-1 bg-warning-100 text-warning-700 rounded-full text-xs font-medium">
                  Modern Tools
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Call-to-Action Section */}
      <div className="bg-gradient-to-r from-primary-600 to-accent-600 rounded-2xl p-8 md:p-12 text-center text-white">
        <div className="space-y-6">
          <h2 className="text-3xl md:text-4xl font-bold">
            Ready to Start Your Investment Journey?
          </h2>
          <p className="text-xl opacity-90 max-w-3xl mx-auto">
            Join thousands of investors who are already benefiting from blockchain-powered SACCO investments.
            Start with any package and experience the future of cooperative finance.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="lg"
              variant="secondary"
              className="px-8 py-4 bg-white text-primary-600 hover:bg-gray-100"
              onClick={scrollToPackages}
            >
              View All Packages
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="px-8 py-4 border-white text-white hover:bg-white hover:text-primary-600"
              onClick={scrollToHowItWorks}
            >
              Learn More
            </Button>
          </div>
        </div>
      </div>

      {/* Trust & Security Footer */}
      <div className="border-t border-gray-200 pt-12">
        <div className="text-center space-y-8">
          <h3 className="text-2xl font-semibold text-gray-900">
            Trusted by the Community
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center space-y-2">
              <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto">
                <CheckCircle className="h-6 w-6 text-success-600" />
              </div>
              <h4 className="font-semibold text-gray-900">Smart Contract Audited</h4>
              <p className="text-gray-600 text-sm">Comprehensive security audit completed</p>
            </div>

            <div className="text-center space-y-2">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto">
                <Lock className="h-6 w-6 text-primary-600" />
              </div>
              <h4 className="font-semibold text-gray-900">Secure Vesting</h4>
              <p className="text-gray-600 text-sm">Time-locked smart contracts protect funds</p>
            </div>

            <div className="text-center space-y-2">
              <div className="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center mx-auto">
                <Globe className="h-6 w-6 text-accent-600" />
              </div>
              <h4 className="font-semibold text-gray-900">BSC Network</h4>
              <p className="text-gray-600 text-sm">Built on proven Binance Smart Chain</p>
            </div>

            <div className="text-center space-y-2">
              <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto">
                <Shield className="h-6 w-6 text-warning-600" />
              </div>
              <h4 className="font-semibold text-gray-900">Transparent Operations</h4>
              <p className="text-gray-600 text-sm">All transactions visible on blockchain</p>
            </div>
          </div>

          <div className="text-center text-gray-500 text-sm">
            <p>
              BlockCoop is a decentralized platform. Always do your own research and invest responsibly.
            </p>
          </div>
        </div>
      </div>

    </div>
  );
}