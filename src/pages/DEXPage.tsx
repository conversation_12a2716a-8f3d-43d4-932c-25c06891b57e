import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Badge } from '../components/ui/Badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { 
  ArrowUpDown, 
  Plus, 
  Minus,
  Info,
  TrendingUp,
  Droplets,
  ExternalLink,
  RefreshCw
} from 'lucide-react';
import { useWeb3 } from '../providers/Web3Provider';
import { useRefreshContext } from '../contexts/RefreshContext';
import { SwapCard } from '../components/dex/SwapCard';
import { AddLiquidityCard } from '../components/dex/AddLiquidityCard';
import { PoolInfoCard, PoolStatsWidget } from '../components/dex/PoolInfoCard';
import { LiquidityRedemptionCard } from '../components/liquidity/LiquidityRedemptionCard';
import { getPancakeSwapUrl, appKitConfig } from '../lib/appkit';
import toast from 'react-hot-toast';

export function DEXPage() {
  const { isConnected, isCorrectNetwork } = useWeb3();
  const { refreshBalances, refreshPurchaseHistory } = useRefreshContext();
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState('swap');

  // Handle URL parameters for tab switching
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam && ['swap', 'add', 'remove', 'info'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [searchParams]);

  // Update URL when tab changes
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    setSearchParams({ tab });
  };

  // Handle successful transactions
  const handleTransactionSuccess = async () => {
    try {
      // Trigger refresh for all relevant data
      await Promise.all([
        refreshBalances(),
        refreshPurchaseHistory()
      ]);

      // Also trigger pool info refresh via custom event
      window.dispatchEvent(new CustomEvent('refreshPoolInfo', {
        detail: { timestamp: Date.now() }
      }));

      toast.success('Transaction completed! Data will refresh shortly.');
    } catch (error) {
      console.error('Error refreshing data after transaction:', error);
      toast.success('Transaction completed!');
    }
  };

  const openPancakeSwap = () => {
    const url = getPancakeSwapUrl('swap', appKitConfig.contracts.share, appKitConfig.contracts.usdt);
    window.open(url, '_blank');
  };

  const openAddLiquidityOnPancakeSwap = () => {
    const url = getPancakeSwapUrl('add', appKitConfig.contracts.share, appKitConfig.contracts.usdt);
    window.open(url, '_blank');
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">DEX Trading</h1>
            <p className="text-gray-600 mt-1">
              Trade BLOCKS and USDT, manage liquidity positions
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={openPancakeSwap}
              className="hidden sm:flex"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              PancakeSwap
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Trigger pool info refresh via custom event
                window.dispatchEvent(new CustomEvent('refreshPoolInfo', {
                  detail: { timestamp: Date.now() }
                }));
              }}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <PoolStatsWidget />
          
          <div className="p-4 bg-green-50 rounded-lg">
            <div className="flex items-center gap-2 text-green-600 mb-2">
              <TrendingUp className="h-4 w-4" />
              <span className="text-sm font-medium">Trading</span>
            </div>
            <div className="text-xs text-green-700">
              <div>Instant swaps between ShareToken and USDT</div>
              <div className="mt-1">Low slippage • MEV protection</div>
            </div>
          </div>

          <div className="p-4 bg-purple-50 rounded-lg">
            <div className="flex items-center gap-2 text-purple-600 mb-2">
              <Droplets className="h-4 w-4" />
              <span className="text-sm font-medium">Liquidity</span>
            </div>
            <div className="text-xs text-purple-700">
              <div>Earn fees by providing liquidity</div>
              <div className="mt-1">Add/Remove • Real-time quotes</div>
            </div>
          </div>
        </div>

        {/* Connection Status */}
        {!isConnected && (
          <Card className="mb-6 border-yellow-200 bg-yellow-50">
            <CardContent className="py-4">
              <div className="flex items-center gap-2 text-yellow-800">
                <Info className="h-4 w-4" />
                <span className="text-sm">
                  Connect your wallet to start trading and managing liquidity positions
                </span>
              </div>
            </CardContent>
          </Card>
        )}

        {isConnected && !isCorrectNetwork && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="py-4">
              <div className="flex items-center gap-2 text-red-800">
                <Info className="h-4 w-4" />
                <span className="text-sm">
                  Please switch to BSC Testnet to use DEX functionality
                </span>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Trading Interface */}
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="swap" className="flex items-center gap-2">
                <ArrowUpDown className="h-4 w-4" />
                <span className="hidden sm:inline">Swap</span>
              </TabsTrigger>
              <TabsTrigger value="add" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                <span className="hidden sm:inline">Add</span>
              </TabsTrigger>
              <TabsTrigger value="remove" className="flex items-center gap-2">
                <Minus className="h-4 w-4" />
                <span className="hidden sm:inline">Remove</span>
              </TabsTrigger>
              <TabsTrigger value="info" className="flex items-center gap-2">
                <Info className="h-4 w-4" />
                <span className="hidden sm:inline">Info</span>
              </TabsTrigger>
            </TabsList>

            <div className="mt-6">
              <TabsContent value="swap" className="space-y-4">
                <SwapCard onSwapComplete={handleTransactionSuccess} />
                
                {/* External Link */}
                <div className="text-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={openPancakeSwap}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Trade on PancakeSwap
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="add" className="space-y-4">
                <AddLiquidityCard onLiquidityAdded={handleTransactionSuccess} />
                
                {/* External Link */}
                <div className="text-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={openAddLiquidityOnPancakeSwap}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Add Liquidity on PancakeSwap
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="remove" className="space-y-4">
                <LiquidityRedemptionCard onRedemptionComplete={handleTransactionSuccess} />
                
                <Card>
                  <CardContent className="py-4">
                    <div className="text-sm text-gray-600 text-center">
                      <p className="mb-2">
                        <strong>Enhanced Liquidity Removal:</strong>
                      </p>
                      <p>
                        This feature removes your LP tokens from the PancakeSwap pool and 
                        returns both ShareToken and USDT to your wallet with slippage protection.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="info" className="space-y-4">
                <PoolInfoCard showUserPosition={true} />
              </TabsContent>
            </div>
          </Tabs>
        </div>

        {/* Right Column - Pool Information */}
        <div className="space-y-6">
          <PoolInfoCard showUserPosition={isConnected} />
          
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Quick Actions</h3>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => handleTabChange('swap')}
              >
                <ArrowUpDown className="h-4 w-4 mr-2" />
                Swap Tokens
              </Button>

              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => handleTabChange('add')}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Liquidity
              </Button>

              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => handleTabChange('remove')}
              >
                <Minus className="h-4 w-4 mr-2" />
                Remove Liquidity
              </Button>
              
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={openPancakeSwap}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                PancakeSwap
              </Button>
            </CardContent>
          </Card>

          {/* Help Card */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Need Help?</h3>
            </CardHeader>
            <CardContent className="space-y-2 text-sm text-gray-600">
              <p>
                <strong>Swapping:</strong> Trade between BLOCKS and USDT instantly
              </p>
              <p>
                <strong>Adding Liquidity:</strong> Provide both tokens to earn LP tokens and fees
              </p>
              <p>
                <strong>Removing Liquidity:</strong> Burn LP tokens to get your tokens back
              </p>
              <p>
                <strong>Slippage:</strong> Maximum price change you'll accept for a trade
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
