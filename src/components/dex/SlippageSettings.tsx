import React, { useState } from 'react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Label } from '../ui/label';
import { Badge } from '../ui/Badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Settings, AlertTriangle, Info } from 'lucide-react';

interface SlippageSettingsProps {
  slippage: number;
  onSlippageChange: (slippage: number) => void;
  className?: string;
}

const PRESET_SLIPPAGES = [0.1, 0.5, 1.0, 2.0];

export function SlippageSettings({ slippage, onSlippageChange, className = '' }: SlippageSettingsProps) {
  const [customSlippage, setCustomSlippage] = useState(slippage.toString());
  const [isCustom, setIsCustom] = useState(!PRESET_SLIPPAGES.includes(slippage));

  const handlePresetClick = (preset: number) => {
    setIsCustom(false);
    setCustomSlippage(preset.toString());
    onSlippageChange(preset);
  };

  const handleCustomChange = (value: string) => {
    setCustomSlippage(value);
    const numValue = parseFloat(value);
    
    if (!isNaN(numValue) && numValue >= 0.01 && numValue <= 50) {
      onSlippageChange(numValue);
    }
  };

  const handleCustomFocus = () => {
    setIsCustom(true);
  };

  const getSlippageWarning = () => {
    if (slippage < 0.1) {
      return { type: 'error', message: 'Slippage too low - transaction may fail' };
    } else if (slippage > 5) {
      return { type: 'warning', message: 'High slippage - you may lose value to MEV' };
    } else if (slippage > 1) {
      return { type: 'info', message: 'Moderate slippage tolerance' };
    }
    return null;
  };

  const warning = getSlippageWarning();

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center gap-2">
        <Settings className="h-4 w-4 text-gray-600" />
        <Label className="text-sm font-medium">Slippage Tolerance</Label>
      </div>

      {/* Preset Buttons */}
      <div className="flex gap-2">
        {PRESET_SLIPPAGES.map((preset) => (
          <Button
            key={preset}
            variant={!isCustom && slippage === preset ? "default" : "outline"}
            size="sm"
            onClick={() => handlePresetClick(preset)}
            className="flex-1"
          >
            {preset}%
          </Button>
        ))}
      </div>

      {/* Custom Input */}
      <div className="space-y-2">
        <div className="relative">
          <Input
            type="number"
            step="0.01"
            min="0.01"
            max="50"
            placeholder="Custom"
            value={customSlippage}
            onChange={(e) => handleCustomChange(e.target.value)}
            onFocus={handleCustomFocus}
            className={`pr-8 ${isCustom ? 'border-blue-500' : ''}`}
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <span className="text-xs text-gray-500">%</span>
          </div>
        </div>
      </div>

      {/* Warning Messages */}
      {warning && (
        <Alert className={
          warning.type === 'error' ? 'border-red-200 bg-red-50' :
          warning.type === 'warning' ? 'border-yellow-200 bg-yellow-50' :
          'border-blue-200 bg-blue-50'
        }>
          {warning.type === 'error' ? (
            <AlertTriangle className="h-4 w-4 text-red-600" />
          ) : warning.type === 'warning' ? (
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
          ) : (
            <Info className="h-4 w-4 text-blue-600" />
          )}
          <AlertDescription className={
            warning.type === 'error' ? 'text-red-700' :
            warning.type === 'warning' ? 'text-yellow-700' :
            'text-blue-700'
          }>
            {warning.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Info Text */}
      <div className="text-xs text-gray-500">
        <p>
          Slippage tolerance is the maximum price change you're willing to accept.
          Higher values reduce the chance of failed transactions but increase the risk of unfavorable trades.
        </p>
      </div>
    </div>
  );
}

// Hook for managing slippage state
export function useSlippageSettings(initialSlippage: number = 0.5) {
  const [slippage, setSlippage] = useState(initialSlippage);

  const isHighSlippage = slippage > 2;
  const isVeryHighSlippage = slippage > 5;
  const isLowSlippage = slippage < 0.1;

  const getSlippageColor = () => {
    if (isLowSlippage || isVeryHighSlippage) return 'text-red-600';
    if (isHighSlippage) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getSlippageBadgeVariant = () => {
    if (isLowSlippage || isVeryHighSlippage) return 'destructive';
    if (isHighSlippage) return 'secondary';
    return 'default';
  };

  return {
    slippage,
    setSlippage,
    isHighSlippage,
    isVeryHighSlippage,
    isLowSlippage,
    getSlippageColor,
    getSlippageBadgeVariant,
  };
}
