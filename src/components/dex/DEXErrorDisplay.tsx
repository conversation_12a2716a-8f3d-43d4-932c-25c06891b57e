import React from 'react';
import { Alert, AlertDescription } from '../ui/alert';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { 
  AlertTriangle, 
  XCircle, 
  RefreshCw, 
  ExternalLink,
  Info,
  Wifi,
  DollarSign,
  Clock,
  Zap
} from 'lucide-react';
import { DEXError, DEXErrorType } from '../../lib/dexErrorHandling';

interface DEXErrorDisplayProps {
  error: DEXError;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
  compact?: boolean;
}

export function DEXErrorDisplay({ 
  error, 
  onRetry, 
  onDismiss, 
  className = '',
  compact = false 
}: DEXErrorDisplayProps) {
  const getErrorIcon = () => {
    switch (error.type) {
      case DEXErrorType.NETWORK_ERROR:
        return <Wifi className="h-4 w-4" />;
      case DEXErrorType.INSUFFICIENT_BALANCE:
        return <DollarSign className="h-4 w-4" />;
      case DEXErrorType.DEADLINE_EXCEEDED:
        return <Clock className="h-4 w-4" />;
      case DEXErrorType.USER_REJECTED:
        return <XCircle className="h-4 w-4" />;
      case DEXErrorType.SLIPPAGE_EXCEEDED:
        return <Zap className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getErrorColor = () => {
    switch (error.type) {
      case DEXErrorType.USER_REJECTED:
        return 'border-gray-200 bg-gray-50 text-gray-700';
      case DEXErrorType.NETWORK_ERROR:
        return 'border-blue-200 bg-blue-50 text-blue-700';
      case DEXErrorType.INSUFFICIENT_BALANCE:
      case DEXErrorType.INSUFFICIENT_ALLOWANCE:
        return 'border-yellow-200 bg-yellow-50 text-yellow-700';
      case DEXErrorType.SLIPPAGE_EXCEEDED:
      case DEXErrorType.PRICE_IMPACT_TOO_HIGH:
        return 'border-orange-200 bg-orange-50 text-orange-700';
      default:
        return 'border-red-200 bg-red-50 text-red-700';
    }
  };

  const getErrorBadgeVariant = () => {
    switch (error.type) {
      case DEXErrorType.USER_REJECTED:
        return 'secondary';
      case DEXErrorType.NETWORK_ERROR:
        return 'default';
      case DEXErrorType.INSUFFICIENT_BALANCE:
      case DEXErrorType.INSUFFICIENT_ALLOWANCE:
        return 'secondary';
      default:
        return 'destructive';
    }
  };

  const getErrorTypeLabel = () => {
    switch (error.type) {
      case DEXErrorType.NETWORK_ERROR:
        return 'Network Issue';
      case DEXErrorType.INSUFFICIENT_BALANCE:
        return 'Insufficient Balance';
      case DEXErrorType.INSUFFICIENT_ALLOWANCE:
        return 'Approval Needed';
      case DEXErrorType.SLIPPAGE_EXCEEDED:
        return 'Slippage Too High';
      case DEXErrorType.DEADLINE_EXCEEDED:
        return 'Transaction Expired';
      case DEXErrorType.PAIR_NOT_EXISTS:
        return 'Pair Not Found';
      case DEXErrorType.INSUFFICIENT_LIQUIDITY:
        return 'Low Liquidity';
      case DEXErrorType.TRANSACTION_FAILED:
        return 'Transaction Failed';
      case DEXErrorType.USER_REJECTED:
        return 'User Cancelled';
      case DEXErrorType.INVALID_AMOUNT:
        return 'Invalid Amount';
      case DEXErrorType.PRICE_IMPACT_TOO_HIGH:
        return 'High Price Impact';
      default:
        return 'Error';
    }
  };

  if (compact) {
    return (
      <div className={`flex items-center gap-2 p-2 rounded-lg ${getErrorColor()} ${className}`}>
        {getErrorIcon()}
        <span className="text-sm font-medium">{error.userMessage}</span>
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="ml-auto h-6 w-6 p-0"
          >
            <XCircle className="h-3 w-3" />
          </Button>
        )}
      </div>
    );
  }

  return (
    <Alert className={`${getErrorColor()} ${className}`}>
      <div className="flex items-start gap-3">
        {getErrorIcon()}
        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-2">
            <AlertDescription className="font-medium">
              {error.userMessage}
            </AlertDescription>
            <Badge variant={getErrorBadgeVariant()} className="text-xs">
              {getErrorTypeLabel()}
            </Badge>
          </div>
          
          {error.suggestedAction && (
            <div className="text-sm opacity-90">
              <strong>Suggestion:</strong> {error.suggestedAction}
            </div>
          )}

          {/* Action buttons */}
          <div className="flex items-center gap-2 pt-2">
            {error.actionable && onRetry && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRetry}
                className="h-8 text-xs"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Try Again
              </Button>
            )}
            
            {error.type === DEXErrorType.NETWORK_ERROR && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
                className="h-8 text-xs"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Refresh Page
              </Button>
            )}
            
            {(error.type === DEXErrorType.INSUFFICIENT_LIQUIDITY || 
              error.type === DEXErrorType.PAIR_NOT_EXISTS) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open('https://pancakeswap.finance', '_blank')}
                className="h-8 text-xs"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                PancakeSwap
              </Button>
            )}
            
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                className="h-8 text-xs ml-auto"
              >
                <XCircle className="h-3 w-3 mr-1" />
                Dismiss
              </Button>
            )}
          </div>
        </div>
      </div>
    </Alert>
  );
}

// Loading state component
interface DEXLoadingStateProps {
  operation: string;
  message?: string;
  className?: string;
}

export function DEXLoadingState({ 
  operation, 
  message, 
  className = '' 
}: DEXLoadingStateProps) {
  return (
    <div className={`flex items-center justify-center p-6 ${className}`}>
      <div className="flex items-center gap-3 text-blue-600">
        <RefreshCw className="h-5 w-5 animate-spin" />
        <div>
          <div className="font-medium">{operation}</div>
          {message && (
            <div className="text-sm text-blue-500">{message}</div>
          )}
        </div>
      </div>
    </div>
  );
}

// Success state component
interface DEXSuccessStateProps {
  message: string;
  txHash?: string;
  onDismiss?: () => void;
  className?: string;
}

export function DEXSuccessState({ 
  message, 
  txHash, 
  onDismiss, 
  className = '' 
}: DEXSuccessStateProps) {
  const explorerUrl = txHash 
    ? `https://testnet.bscscan.com/tx/${txHash}`
    : null;

  return (
    <Alert className={`border-green-200 bg-green-50 text-green-700 ${className}`}>
      <div className="flex items-start gap-3">
        <div className="rounded-full bg-green-100 p-1">
          <RefreshCw className="h-4 w-4 text-green-600" />
        </div>
        <div className="flex-1">
          <AlertDescription className="font-medium">
            {message}
          </AlertDescription>
          
          <div className="flex items-center gap-2 mt-2">
            {explorerUrl && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(explorerUrl, '_blank')}
                className="h-8 text-xs"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                View Transaction
              </Button>
            )}
            
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                className="h-8 text-xs ml-auto"
              >
                <XCircle className="h-3 w-3 mr-1" />
                Dismiss
              </Button>
            )}
          </div>
        </div>
      </div>
    </Alert>
  );
}
