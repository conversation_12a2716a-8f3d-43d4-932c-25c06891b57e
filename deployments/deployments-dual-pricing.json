{"timestamp": "2025-07-10T22:08:46.542Z", "network": "bsctestnet", "deployer": "0x842d803eB7d05D6Aa2DdB8c3Eb912e6d97ce31C4", "version": "dual-pricing-system", "features": ["Per-package exchange rates for user token allocation", "Global target price for liquidity pool operations", "Dual pricing system separates user-facing rates from LP pricing", "Enhanced admin controls for both pricing mechanisms"], "contracts": {"PackageManagerV2_1": "0x495C7FB1c74e87aDD4b1cef1F88854Dc1257e0F2", "BLOCKS": "0x739D13ac638974DcDc92233cf7B1aa08ffF7728f", "BLOCKS_LP": "0xaC05CA16038a21c8e6a20E6428B3E30B7FdD9436", "VestingVault": "0x084c34dc7eBC1c4c10D4B116e222D02A4c1286DA", "SwapTaxManager": "0xbCE77b95a011e114a8b003e7f211ddf9c3eF381f", "USDT": "0x350eBe9e8030B5C2e70f831b82b92E44569736fF", "PancakeRouter": "0xD99D1c33F9fC3444f8101754aBC46c52416550D1", "Treasury": "0x842d803eB7d05D6Aa2DdB8c3Eb912e6d97ce31C4"}, "pricing": {"globalTargetPrice": "2.0", "testPackages": [{"name": "Starter Package", "exchangeRate": "1.5"}, {"name": "Growth Package", "exchangeRate": "1.8"}, {"name": "Premium Package", "exchangeRate": "2.2"}]}, "gasUsed": {"BLOCKS": "1108268", "BLOCKS_LP": "837422", "VestingVault": "635612", "SwapTaxManager": "430310", "PackageManagerV2_1": "3766753"}}