{"timestamp": "2025-07-14T14:55:21.476Z", "network": "bsctestnet", "contracts": {"PackageManagerV2_1": {"address": "0xa1a274557FE7dC408d5E9ADCf48EB3CB2bbe5358", "verified": true, "constructorArgs": ["0x350eBe9e8030B5C2e70f831b82b92E44569736fF", "0x739D13ac638974DcDc92233cf7B1aa08ffF7728f", "0xaC05CA16038a21c8e6a20E6428B3E30B7FdD9436", "0x084c34dc7eBC1c4c10D4B116e222D02A4c1286DA", "0xD99D1c33F9fC3444f8101754aBC46c52416550D1", "0x6725F303b657a9451d8BA641348b6761A6CC7a17", "0x842d803eB7d05D6Aa2DdB8c3Eb912e6d97ce31C4", "0xbCE77b95a011e114a8b003e7f211ddf9c3eF381f", "0x842d803eB7d05D6Aa2DdB8c3Eb912e6d97ce31C4", "2000000000000000000"], "fix": "Referral calculation bug - now uses totalUserTokens instead of totalTokens"}}, "bscscanUrl": "https://testnet.bscscan.com/address/0xa1a274557FE7dC408d5E9ADCf48EB3CB2bbe5358#code"}