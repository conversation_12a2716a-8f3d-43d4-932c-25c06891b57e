const { ethers } = require("hardhat");
const fs = require("fs");

async function main() {
  console.log("🚀 Deploying Enhanced BLOCKS Token with DEX Tax Support...\n");

  const [deployer] = await ethers.getSigners();
  console.log("📋 Deploying with account:", deployer.address);
  console.log("💰 Account balance:", ethers.formatEther(await ethers.provider.getBalance(deployer.address)), "BNB\n");

  // Load existing deployment data
  const deployFile = "deployments/deployments-fresh-v2.json";
  if (!fs.existsSync(deployFile)) {
    throw new Error(`Deployment file not found: ${deployFile}`);
  }

  const existingData = JSON.parse(fs.readFileSync(deployFile));
  console.log("📦 Using existing contracts:");
  console.log("SwapTaxManager:", existingData.contracts.SwapTaxManager);
  console.log("BLOCKS-LP:", existingData.contracts["BLOCKS-LP"]);
  console.log("VestingVault:", existingData.contracts.VestingVault);
  console.log("PackageManagerV2_1:", existingData.contracts.PackageManagerV2_1);

  // Configuration
  const ADMIN_ADDRESS = existingData.admins.primary;
  const ADDITIONAL_ADMIN = existingData.admins.additional;
  const SWAP_TAX_MANAGER = existingData.contracts.SwapTaxManager;
  const TREASURY_ADDRESS = existingData.contracts.Treasury;

  console.log("\n📦 Step 1: Deploying Enhanced BLOCKS token...");
  const BLOCKS = await ethers.getContractFactory("BLOCKS");
  const blocks = await BLOCKS.deploy(
    "BlockCoop Sacco Share Token", // name
    "BLOCKS",                      // symbol
    ADMIN_ADDRESS,                 // admin
    SWAP_TAX_MANAGER              // swapTaxManager
  );
  await blocks.waitForDeployment();
  const blocksAddress = await blocks.getAddress();
  console.log("✅ Enhanced BLOCKS deployed to:", blocksAddress);

  console.log("\n⚙️ Step 2: Configuring roles and permissions...");
  
  // Grant additional admin role
  console.log("👤 Granting admin role to additional admin...");
  let tx = await blocks.grantRole(await blocks.DEFAULT_ADMIN_ROLE(), ADDITIONAL_ADMIN);
  await tx.wait();
  console.log("✅ Additional admin role granted");

  // Grant TAX_MANAGER_ROLE to both admins for AMM management
  console.log("🔧 Granting TAX_MANAGER_ROLE to admins...");
  const TAX_MANAGER_ROLE = await blocks.TAX_MANAGER_ROLE();
  
  tx = await blocks.grantRole(TAX_MANAGER_ROLE, ADMIN_ADDRESS);
  await tx.wait();
  console.log("✅ TAX_MANAGER_ROLE granted to primary admin");
  
  tx = await blocks.grantRole(TAX_MANAGER_ROLE, ADDITIONAL_ADMIN);
  await tx.wait();
  console.log("✅ TAX_MANAGER_ROLE granted to additional admin");

  // Grant MINTER_ROLE to PackageManager
  console.log("🏭 Granting MINTER_ROLE to PackageManager...");
  const MINTER_ROLE = await blocks.MINTER_ROLE();
  tx = await blocks.grantRole(MINTER_ROLE, existingData.contracts.PackageManagerV2_1);
  await tx.wait();
  console.log("✅ MINTER_ROLE granted to PackageManager");

  console.log("\n💾 Step 3: Saving deployment information...");

  const deploymentData = {
    network: existingData.network,
    chainId: existingData.chainId,
    deployer: deployer.address,
    timestamp: new Date().toISOString(),
    version: "enhanced-blocks-with-dex-tax",
    contracts: {
      ...existingData.contracts,
      BLOCKS: blocksAddress, // Replace old BLOCKS with new enhanced version
    },
    admins: existingData.admins,
    externalContracts: existingData.externalContracts,
    taxConfiguration: {
      buyTaxKey: await blocks.BUY_TAX_KEY(),
      sellTaxKey: await blocks.SELL_TAX_KEY(),
      swapTaxManager: SWAP_TAX_MANAGER
    }
  };

  const outputFile = "deployments/deployments-enhanced-blocks.json";
  fs.writeFileSync(outputFile, JSON.stringify(deploymentData, null, 2));
  console.log("✅ Deployment data saved to:", outputFile);

  console.log("\n🎯 Step 4: Deployment Summary");
  console.log("=====================================");
  console.log("Enhanced BLOCKS Token:", blocksAddress);
  console.log("SwapTaxManager:", SWAP_TAX_MANAGER);
  console.log("Treasury:", TREASURY_ADDRESS);
  console.log("Primary Admin:", ADMIN_ADDRESS);
  console.log("Additional Admin:", ADDITIONAL_ADMIN);
  console.log("=====================================");

  console.log("\n📋 Next Steps:");
  console.log("1. Configure DEX tax buckets using configure-dex-taxes.cjs");
  console.log("2. Set up AMM pair addresses for tax detection");
  console.log("3. Update PackageManager to use new BLOCKS token");
  console.log("4. Test DEX tax functionality");
  console.log("5. Migrate liquidity from old to new BLOCKS token");

  console.log("\n🔗 Verification Commands:");
  console.log(`npx hardhat verify --network bsctestnet ${blocksAddress} "BlockCoop Sacco Share Token" "BLOCKS" "${ADMIN_ADDRESS}" "${SWAP_TAX_MANAGER}"`);

  console.log("\n🎉 Enhanced BLOCKS deployment completed successfully!");
}

main().catch((error) => {
  console.error("\n❌ Deployment failed:");
  console.error(error);
  process.exit(1);
});
