const { ethers } = require("hardhat");
const fs = require("fs");

async function main() {
  console.log("🔧 Configuring DEX Tax Buckets for Enhanced BLOCKS Token...\n");

  const [deployer] = await ethers.getSigners();
  console.log("📋 Configuring with account:", deployer.address);
  console.log("💰 Account balance:", ethers.formatEther(await ethers.provider.getBalance(deployer.address)), "BNB\n");

  // Load deployment data
  const deployFile = "deployments/deployments-enhanced-blocks.json";
  if (!fs.existsSync(deployFile)) {
    throw new Error(`Enhanced BLOCKS deployment file not found: ${deployFile}. Please run deploy-blocks-with-dex-tax.cjs first.`);
  }

  const data = JSON.parse(fs.readFileSync(deployFile));
  
  console.log("📍 Using contracts:");
  console.log("Enhanced BLOCKS:", data.contracts.BLOCKS);
  console.log("SwapTaxManager:", data.contracts.SwapTaxManager);
  console.log("Treasury:", data.contracts.Treasury);
  
  // Get contract instances
  const blocks = await ethers.getContractAt("BLOCKS", data.contracts.BLOCKS);
  const taxManager = await ethers.getContractAt("SwapTaxManager", data.contracts.SwapTaxManager);
  
  console.log("\n⚙️ Step 1: Configuring DEX tax buckets...");
  
  // Get tax keys from the BLOCKS contract
  const buyTaxKey = await blocks.BUY_TAX_KEY();
  const sellTaxKey = await blocks.SELL_TAX_KEY();
  
  console.log("🔑 Tax Keys:");
  console.log("Buy Tax Key:", buyTaxKey);
  console.log("Sell Tax Key:", sellTaxKey);
  
  // Configure buy tax bucket (1% to treasury)
  console.log("\n💰 Setting buy tax: 1% to treasury...");
  let tx = await taxManager.setBucket(
    buyTaxKey,
    100, // 1% in basis points
    data.contracts.Treasury
  );
  await tx.wait();
  console.log("✅ Buy tax bucket configured");
  
  // Configure sell tax bucket (1% to treasury)
  console.log("💰 Setting sell tax: 1% to treasury...");
  tx = await taxManager.setBucket(
    sellTaxKey,
    100, // 1% in basis points
    data.contracts.Treasury
  );
  await tx.wait();
  console.log("✅ Sell tax bucket configured");
  
  console.log("\n🏭 Step 2: Setting up AMM pair addresses...");
  
  // Get PancakeSwap factory to find BLOCKS/USDT pair
  const factoryAddress = data.externalContracts.PancakeFactory;
  const factory = await ethers.getContractAt("IPancakeFactory", factoryAddress);
  
  // Check if BLOCKS/USDT pair exists
  const usdtAddress = data.externalContracts.USDT;
  const blocksAddress = data.contracts.BLOCKS;
  
  console.log("🔍 Checking for BLOCKS/USDT pair...");
  const pairAddress = await factory.getPair(blocksAddress, usdtAddress);
  
  if (pairAddress === ethers.ZeroAddress) {
    console.log("⚠️  BLOCKS/USDT pair does not exist yet");
    console.log("📝 Pair will need to be created and configured manually after liquidity is added");
  } else {
    console.log("✅ BLOCKS/USDT pair found:", pairAddress);
    console.log("🔧 Setting AMM status for pair...");
    
    tx = await blocks.setAMMStatus(pairAddress, true);
    await tx.wait();
    console.log("✅ AMM status set for BLOCKS/USDT pair");
  }
  
  console.log("\n🔍 Step 3: Verifying configuration...");
  
  try {
    // Verify tax buckets
    const buyTax = await taxManager.buckets(buyTaxKey);
    const sellTax = await taxManager.buckets(sellTaxKey);
    
    console.log("💰 Buy Tax Configuration:");
    console.log(`   Rate: ${buyTax[0]} BPS (${Number(buyTax[0]) / 100}%)`);
    console.log(`   Recipient: ${buyTax[1]}`);
    
    console.log("💰 Sell Tax Configuration:");
    console.log(`   Rate: ${sellTax[0]} BPS (${Number(sellTax[0]) / 100}%)`);
    console.log(`   Recipient: ${sellTax[1]}`);
    
    // Verify AMM status if pair exists
    if (pairAddress !== ethers.ZeroAddress) {
      const isAMM = await blocks.isAMM(pairAddress);
      console.log(`🏭 BLOCKS/USDT Pair AMM Status: ${isAMM}`);
    }
    
    // Verify roles
    const TAX_MANAGER_ROLE = await blocks.TAX_MANAGER_ROLE();
    const hasRole = await blocks.hasRole(TAX_MANAGER_ROLE, deployer.address);
    console.log(`🔑 Deployer has TAX_MANAGER_ROLE: ${hasRole}`);
    
  } catch (error) {
    console.error("❌ Verification error:", error.message);
  }

  console.log("\n🎉 DEX tax configuration completed successfully!");
  
  console.log("\n📋 Configuration Summary:");
  console.log("=====================================");
  console.log("Buy Tax: 1% → Treasury");
  console.log("Sell Tax: 1% → Treasury");
  console.log("Treasury Address:", data.contracts.Treasury);
  console.log("SwapTaxManager:", data.contracts.SwapTaxManager);
  console.log("Enhanced BLOCKS:", data.contracts.BLOCKS);
  if (pairAddress !== ethers.ZeroAddress) {
    console.log("BLOCKS/USDT Pair:", pairAddress, "(AMM enabled)");
  }
  console.log("=====================================");
  
  console.log("\n🔗 Next Steps:");
  console.log("1. Update PackageManager to use new BLOCKS token address");
  console.log("2. Create BLOCKS/USDT liquidity pool if not exists");
  console.log("3. Test buy/sell transactions to verify tax collection");
  console.log("4. Monitor treasury balance for tax accumulation");
  console.log("5. Add additional AMM pairs as needed using setAMMStatus()");
  
  if (pairAddress === ethers.ZeroAddress) {
    console.log("\n⚠️  Important: BLOCKS/USDT pair not found!");
    console.log("After creating liquidity, run:");
    console.log(`await blocks.setAMMStatus("PAIR_ADDRESS", true);`);
  }
}

main().catch((error) => {
  console.error("\n❌ Configuration failed:");
  console.error(error);
  process.exit(1);
});
