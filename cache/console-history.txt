.exit
console.log("Deployer has MINTER_ROLE now:", hasRoleNow);
const hasRoleNow = await blocks.hasRole(MINTER_ROLE, deployer.address);
await blocks.grantRole(MINTER_ROLE, deployer.address);
console.log("Deployer has MINTER_ROLE:", hasRole);
const hasRole = await blocks.hasRole(MINTER_ROLE, deployer.address);
const MINTER_ROLE = await blocks.MINTER_ROLE();
const [deployer] = await ethers.getSigners();
const blocks = await ethers.getContractAt("BLOCKS", "******************************************");
.exit
pm.interface.getFunction("redeemWithLiquidityRemoval")
pm.interface.getFunction("getRedemptionPreview")
await pm.getRedemptionPreview(ethers.parseEther("1"))
const pm = await ethers.getContractAt("PackageManagerV2_1", "******************************************")
.exit
ethers.formatUnits(pkg0.entryUSDT, 6)
pkg0.name
const pkg0 = await pm.getPackage(0)
await pm.getActivePackageIds()
await pm.getPackageCount()
const pm = await ethers.getContractAt("PackageManagerV2_1", "******************************************")