{"_format": "hh-sol-cache-2", "files": {"/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/contracts/BlockCoopV2.sol": {"lastModificationDate": 1752023793565, "contentHash": "942afbda9e5b8d7af7b95130a0962835", "sourceName": "contracts/BlockCoopV2.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "@openzeppelin/contracts/access/AccessControl.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/utils/Pausable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["BLOCKS", "BLOCKS_LP", "IPancakeRouter", "ISwapTaxManager", "SwapTaxManager", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/access/AccessControl.sol": {"lastModificationDate": 1751476701042, "contentHash": "849b15469d8e2bd01b49e6c632e448e7", "sourceName": "@openzeppelin/contracts/access/AccessControl.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IAccessControl.sol", "../utils/Context.sol", "../utils/introspection/ERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["AccessControl"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"lastModificationDate": 1751476708274, "contentHash": "190613e556d509d9e9a0ea43dc5d891d", "sourceName": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"lastModificationDate": 1751476704890, "contentHash": "57d79df281f57bbb1b09214c7914f877", "sourceName": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./extensions/IERC20Metadata.sol", "../../utils/Context.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"lastModificationDate": 1751476708332, "contentHash": "1fb9edee1d763745a62d0b1954998792", "sourceName": "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol", "../../../interfaces/IERC1363.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["SafeERC20"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/utils/Context.sol": {"lastModificationDate": 1751476702685, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/utils/introspection/ERC165.sol": {"lastModificationDate": 1751476704685, "contentHash": "7c03c1e37c3dc24eafb76dc2b8a5c3a6", "sourceName": "@openzeppelin/contracts/utils/introspection/ERC165.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC165"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/access/IAccessControl.sol": {"lastModificationDate": 1751476706786, "contentHash": "80621031deacf7066ec81277f9b1463a", "sourceName": "@openzeppelin/contracts/access/IAccessControl.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IAccessControl"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"lastModificationDate": 1751476707090, "contentHash": "bf0119eb2a570f219729ff38b6cd1df8", "sourceName": "@openzeppelin/contracts/utils/introspection/IERC165.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC165"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1751476707169, "contentHash": "8f19f64d2adadf448840908bbaf431c8", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"lastModificationDate": 1751476703563, "contentHash": "267d92fe4de67b1bdb3302c08f387dbf", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC1155Errors", "IERC20Errors", "IERC721Errors"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"lastModificationDate": 1751476707188, "contentHash": "794db3115001aa372c79326fcfd44b1f", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20Metadata"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol": {"lastModificationDate": 1751476707035, "contentHash": "6b7c5ee7a59c981072a804c99ab0fac9", "sourceName": "@openzeppelin/contracts/interfaces/IERC1363.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC1363"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/interfaces/IERC20.sol": {"lastModificationDate": 1751476707163, "contentHash": "79af12d64eacc7d77b9ee2ac4b4d51ee", "sourceName": "@openzeppelin/contracts/interfaces/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../token/ERC20/IERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": []}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/interfaces/IERC165.sol": {"lastModificationDate": 1751476707074, "contentHash": "f808b485ee0cdc6768ee8385ae5f9a2a", "sourceName": "@openzeppelin/contracts/interfaces/IERC165.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/introspection/IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": []}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@openzeppelin/contracts/utils/Pausable.sol": {"lastModificationDate": 1751476708248, "contentHash": "0d47b53e10b1985efbb396f937626279", "sourceName": "@openzeppelin/contracts/utils/Pausable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Pausable"]}, "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/contracts/PackageManagerV2_1.sol": {"lastModificationDate": 1752504107835, "contentHash": "f94aa9bb26248f454bf4eb74fedac515", "sourceName": "contracts/PackageManagerV2_1.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/AccessControl.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/utils/Pausable.sol", "@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IBLOCKS", "IBLOCKS_LP", "IERC20Decimals", "IPancakeFactory", "IPancakePair", "IPancakeRouter", "ISwapTaxManager", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PackageManagerV2_1"]}}}