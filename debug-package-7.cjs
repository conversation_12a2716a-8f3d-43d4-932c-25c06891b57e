const { ethers } = require('hardhat');
require('dotenv').config();

// Contract addresses from .env
const PACKAGE_MANAGER_ADDRESS = process.env.VITE_PACKAGE_MANAGER_ADDRESS;
const USDT_ADDRESS = process.env.VITE_USDT_ADDRESS;
const USER_ADDRESS = '******************************************';

// PackageManager ABI (minimal for debugging)
const PACKAGE_MANAGER_ABI = [
  "function getPackage(uint256 id) external view returns (tuple(string name, uint256 entryUSDT, uint16 vestBps, uint64 cliff, uint64 duration, uint16 referralBps, bool active, bool exists))",
  "function getPackageIds() external view returns (uint256[])",
  "function usdtToBlocksRateBps() external view returns (uint16)",
  "function nextPackageId() external view returns (uint256)"
];

// USDT ABI (minimal)
const USDT_ABI = [
  "function balanceOf(address account) external view returns (uint256)",
  "function allowance(address owner, address spender) external view returns (uint256)",
  "function decimals() external view returns (uint8)"
];

async function debugPackage7() {
  console.log('🔍 Debugging Package ID 7 Purchase Issue...\n');
  
  try {
    // Get provider
    const provider = new ethers.JsonRpcProvider(process.env.BSC_TESTNET_RPC);
    
    // Create contract instances
    const packageManager = new ethers.Contract(PACKAGE_MANAGER_ADDRESS, PACKAGE_MANAGER_ABI, provider);
    const usdtToken = new ethers.Contract(USDT_ADDRESS, USDT_ABI, provider);
    
    console.log('📋 Contract Information:');
    console.log(`PackageManager: ${PACKAGE_MANAGER_ADDRESS}`);
    console.log(`USDT Token: ${USDT_ADDRESS}`);
    console.log(`User Address: ${USER_ADDRESS}\n`);
    
    // 1. Check if Package ID 7 exists
    console.log('1️⃣ Checking Package ID 7...');
    try {
      const package7 = await packageManager.getPackage(7);
      console.log('✅ Package 7 found:');
      console.log(`   Name: ${package7.name}`);
      console.log(`   Entry USDT (raw): ${package7.entryUSDT.toString()}`);
      console.log(`   Entry USDT (formatted as 6 decimals): ${ethers.formatUnits(package7.entryUSDT, 6)}`);
      console.log(`   Entry USDT (formatted as 18 decimals): ${ethers.formatUnits(package7.entryUSDT, 18)}`);
      console.log(`   Vest BPS: ${package7.vestBps}`);
      console.log(`   Cliff: ${package7.cliff} seconds`);
      console.log(`   Duration: ${package7.duration} seconds`);
      console.log(`   Referral BPS: ${package7.referralBps}`);
      console.log(`   Active: ${package7.active}`);
      console.log(`   Exists: ${package7.exists}\n`);
      
      if (!package7.exists) {
        console.log('❌ Package 7 does not exist!');
        return;
      }
      
      if (!package7.active) {
        console.log('❌ Package 7 is not active!');
        return;
      }
      
    } catch (error) {
      console.log('❌ Error fetching Package 7:', error.message);
      return;
    }
    
    // 2. Check all package IDs
    console.log('2️⃣ Checking all package IDs...');
    try {
      const packageIds = await packageManager.getPackageIds();
      console.log(`Available package IDs: [${packageIds.join(', ')}]`);
      console.log(`Package 7 exists in list: ${packageIds.includes(7n)}\n`);
    } catch (error) {
      console.log('❌ Error fetching package IDs:', error.message);
    }
    
    // 3. Check next package ID
    console.log('3️⃣ Checking next package ID...');
    try {
      const nextId = await packageManager.nextPackageId();
      console.log(`Next package ID: ${nextId}`);
      console.log(`Package 7 should exist: ${7 < nextId}\n`);
    } catch (error) {
      console.log('❌ Error fetching next package ID:', error.message);
    }
    
    // 4. Check global exchange rate
    console.log('4️⃣ Checking global exchange rate...');
    try {
      const exchangeRate = await packageManager.usdtToBlocksRateBps();
      console.log(`Global exchange rate: ${exchangeRate} BPS (${exchangeRate / 100}%)\n`);
    } catch (error) {
      console.log('❌ Error fetching exchange rate:', error.message);
    }
    
    // 5. Check USDT token details
    console.log('5️⃣ Checking USDT token details...');
    try {
      const usdtDecimals = await usdtToken.decimals();
      const userBalance = await usdtToken.balanceOf(USER_ADDRESS);
      const allowance = await usdtToken.allowance(USER_ADDRESS, PACKAGE_MANAGER_ADDRESS);
      
      console.log(`USDT decimals: ${usdtDecimals}`);
      console.log(`User USDT balance (raw): ${userBalance.toString()}`);
      console.log(`User USDT balance (formatted): ${ethers.formatUnits(userBalance, usdtDecimals)}`);
      console.log(`User allowance (raw): ${allowance.toString()}`);
      console.log(`User allowance (formatted): ${ethers.formatUnits(allowance, usdtDecimals)}\n`);
      
      // Check if user has sufficient balance and allowance for Package 7
      const package7 = await packageManager.getPackage(7);
      const packageCost = package7.entryUSDT;
      
      console.log('6️⃣ Balance and Allowance Analysis:');
      console.log(`Package 7 cost (raw): ${packageCost.toString()}`);
      console.log(`Package 7 cost (as 6 decimals): ${ethers.formatUnits(packageCost, 6)}`);
      console.log(`Package 7 cost (as 18 decimals): ${ethers.formatUnits(packageCost, 18)}`);
      console.log(`Has sufficient balance: ${userBalance >= packageCost}`);
      console.log(`Has sufficient allowance: ${allowance >= packageCost}`);
      
      // Determine if this is a legacy package (18 decimals) or new package (6 decimals)
      const isLegacyPackage = packageCost > 1000000000000000n; // > 1e15
      console.log(`Is legacy package (>1e15): ${isLegacyPackage}`);
      
      if (isLegacyPackage) {
        const packageCostUSDT = packageCost / (10n ** 12n); // Convert 18 to 6 decimals
        console.log(`Legacy package - converted cost (6 decimals): ${ethers.formatUnits(packageCostUSDT, 6)}`);
        console.log(`Has sufficient balance for legacy: ${userBalance >= packageCostUSDT}`);
        console.log(`Has sufficient allowance for legacy: ${allowance >= packageCostUSDT}`);
      }
      
    } catch (error) {
      console.log('❌ Error checking USDT details:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Fatal error:', error);
  }
}

// Run the debug script
debugPackage7()
  .then(() => {
    console.log('\n✅ Debug script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Debug script failed:', error);
    process.exit(1);
  });
